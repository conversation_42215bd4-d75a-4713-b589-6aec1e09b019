import React from "react"
import * as ExceptionActions from "../../../Actions/Exception"
import { connect } from "react-redux"
import * as Sessions from '../../../Actions/Sessions';
import axios from "axios";
import Fire from "../../../config/Firebase.jsx";
import CloudStreamingView from "../../../components/CloudStreaming/CloudStreamingView.jsx";

class Lark extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            hasShownEndingToast: false,
            sdkConnected: false,
            authCode: null,
            sdkId: null,
            isGettingAuthCode: false
        };
        this.getAuthCode = this.getAuthCode.bind(this);
        this.handleSDKConnectionSuccess = this.handleSDKConnectionSuccess.bind(this);
        this.handleSDKConnectionError = this.handleSDKConnectionError.bind(this);
    }

    async getAuthCode() {
        if (this.state.isGettingAuthCode || this.state.authCode) {
            return;
        }

        this.setState({ isGettingAuthCode: true });

        try {
            const response = await axios.get('https://ps.propvr.io:8181/util/getAuthCode');
            console.log("Auth code response:", response.data);
            
            if (response.data && response.data.message) {
                this.setState({ 
                    authCode: response.data.message,
                    isGettingAuthCode: false 
                });
                console.log("Auth code set:", response.data.message);
                console.log("Auth code length:", response.data.message.length);
            } else {
                throw new Error("Invalid auth code response");
            }
        } catch (error) {
            console.error("Failed to get auth code:", error);
            this.setState({ isGettingAuthCode: false });
            this.props.CreateToast({
                message: "Authentication Error",
                postmessage: "Failed to get authentication code."
            });
        }
    }

    // Handle SDK connection success
    handleSDKConnectionSuccess(sdkId) {
        console.log("Host SDK connected successfully with SDK ID:", sdkId);
        this.setState({
            sdkConnected: true,
            sdkId: sdkId
        });

        // Store both SDK ID and auth code in Firestore for guests
        this.storeSessionCredentials(sdkId, this.state.authCode);

        this.props.CreateToast({
            message: "LarkXR Connected",
            postmessage: "Successfully connected to the streaming service."
        });
    }

    // Store session credentials in Firestore for guests
    storeSessionCredentials(sdkId, authCode) {
        if (this.props.roomId && sdkId && authCode) {
            const applicationId = this.props.ProjectDetails?.projectSettings?.pixelstreaming?.application_id;

            Fire.firestore()
                .collection('sessions')
                .doc(this.props.roomId)
                .collection('config')
                .doc('data')
                .set({
                    authCode: authCode,
                    applicationId: applicationId,
                    hostConnected: true,
                    timestamp: new Date()
                }, { merge: true })
                .then(() => {
                    console.log("Host: Successfully stored session credentials");
                })
                .catch((error) => {
                    console.error("Host: Failed to store session credentials:", error);
                });
        }
    }

    // Handle SDK connection error
    handleSDKConnectionError(error) {
        console.error("Host SDK connection error:", error);
        this.setState({ sdkConnected: false });

        this.props.CreateToast({
            message: "Connection Error",
            postmessage: "Failed to connect to LarkXR. Please check your configuration."
        });
    }

    componentDidUpdate(prevProps) {
        if (this.props.isNearingEnd && !prevProps.isNearingEnd && !this.state.hasShownEndingToast) {
            this.props.CreateToast({
                message: "Session Ending Soon",
                postmessage: "The session will end in less than 5 minutes."
            });
            this.setState({ hasShownEndingToast: true });
        }
    }

    componentDidMount() {
        console.log("Host LarkXR component mounted with ProjectDetails:", this.props.ProjectDetails);
        
        this.getAuthCode();
    }
    componentWillUnmount() {
    // Mark host as disconnected when component unmounts
    if (this.props.roomId) {
        Fire.firestore()
            .collection('sessions')
            .doc(this.props.roomId)
            .collection('config')
            .doc('data')
            .set({
                hostConnected: false,
                timestamp: new Date()
            }, { merge: true })
            .catch((error) => {
                console.error("Host: Failed to mark as disconnected:", error);
            });
    }
}

    render() {
        // Check if we have the required project details
        if (!this.props.ProjectDetails?.projectSettings?.pixelstreaming?.application_id) {
            return (
                <div style={{
                    position: "absolute",
                    width: "100%",
                    height: "100%",
                    top: 0,
                    left: 0,
                    backgroundColor: "rgba(0, 0, 0, 0.7)",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                }}>
                    <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mb-4"></div>
                    <p className="text-white text-xl font-semibold">Loading Project Configuration...</p>
                    <p className="text-gray-300 mt-2">Please wait while we load the project details</p>
                </div>
            );
        }

        // Check if we're still getting auth code
        if (this.state.isGettingAuthCode) {
            return (
                <div style={{
                    position: "absolute",
                    width: "100%",
                    height: "100%",
                    top: 0,
                    left: 0,
                    backgroundColor: "rgba(0, 0, 0, 0.7)",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                }}>
                    <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mb-4"></div>
                    <p className="text-white text-xl font-semibold">Getting Authentication...</p>
                    <p className="text-gray-300 mt-2">Please wait while we get your auth code</p>
                </div>
            );
        }

        // Check if we have auth code
        if (!this.state.authCode) {
            return (
                <div style={{
                    position: "absolute",
                    width: "100%",
                    height: "100%",
                    top: 0,
                    left: 0,
                    backgroundColor: "rgba(0, 0, 0, 0.7)",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                }}>
                    <div className="text-red-500 text-6xl mb-4">⚠️</div>
                    <p className="text-white text-xl font-semibold">Authentication Required</p>
                    <p className="text-gray-300 mt-2">Failed to get authentication code</p>
                    <button 
                        onClick={this.getAuthCode}
                        className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    >
                        Retry Authentication
                    </button>
                </div>
            );
        }

        // We have everything we need - render the CloudStreamingView
        const applicationId = this.props.ProjectDetails.projectSettings.pixelstreaming.application_id;
        const authCode = this.state.authCode;

        return (
            <CloudStreamingView
                applicationId={applicationId}
                authCode={authCode}
                onConnectionSuccess={this.handleSDKConnectionSuccess}
                onConnectionError={this.handleSDKConnectionError}
            />
        );
    }
}

const mapStateToProps = state => {
    return {
        ProjectDetails: state.Sessions.projectDetails,
        roomId: state.Sessions.sessions?.session_id,
    }
}

const mapDispatchToProps = {
    ...ExceptionActions,
    ...Sessions
}

export default connect(mapStateToProps, mapDispatchToProps)(Lark);
