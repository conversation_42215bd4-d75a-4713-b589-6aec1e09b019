import React from "react"
// import SceneControls from "../../../Tools/ToolComponents/SceneControlsGuest";
import { connect } from "react-redux"
import * as HostActions from "../../../Actions/HostAction"
import * as ExceptionActions from "../../../Actions/Exception"
import * as Sessions from '../../../Actions/Sessions';
import Fire from "../../../config/Firebase.jsx";
import CloudStreamingViewGuest from "../../../components/CloudStreaming/CloudStreamingViewGuest.jsx";

class Lark extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            lock: true,
            hasShownEndingToast: false,
            isLoading: true,
            sdkConnected: false,
            connectionError: false,
            authCode: null,
            sdkId: null,
            isGettingAuthCode: false,
            applicationId: null
        }

        this.handleConnectionSuccess = this.handleConnectionSuccess.bind(this);
        this.handleConnectionError = this.handleConnectionError.bind(this);
    }

    // Wait for host to establish session and get auth code from Firestore
    waitForHostSession() {
        console.log("Guest: Setting up listener for host session...");
        console.log("Guest: Room ID:", this.props.roomId);
        if (this.props.roomId) {
            console.log("Guest: Listening for host session in room:", this.props.roomId);
            // Keep listener active throughout component lifecycle
            this.configListener = Fire.firestore()
                .collection('sessions')
                .doc(this.props.roomId)
                .collection('config')
                .doc('data')
                .onSnapshot((doc) => {
                    if (doc.exists) {
                        const configData = doc.data();
                        console.log("Guest: Received config from host:", configData);

                        // Only need authCode and applicationId - SDK ID is static
                        if (configData.authCode && configData.applicationId && configData.hostConnected) {
                            console.log("Guest: Using host's auth code:", configData.authCode);
                            console.log("Guest: Using host's application ID:", configData.applicationId);

                            this.setState({
                                authCode: configData.authCode,
                                applicationId: configData.applicationId,
                                isGettingAuthCode: false
                            });
                        } else if (configData.hostConnected === false) {
                            // Host disconnected, show loading
                            this.setState({
                                authCode: null,
                                isGettingAuthCode: true
                            });
                        }
                    } else {
                        // No config document, host hasn't connected yet
                        this.setState({
                            authCode: null,
                            isGettingAuthCode: true
                        });
                    }
                }, (error) => {
                    console.error("Guest: Error listening to host config:", error);
                });
        } else {
            console.error("Guest: No room ID available, falling back to own auth code");
        }
    }


    handleConnectionSuccess() {
        console.log("Guest SDK connected successfully");
        this.setState({
            isLoading: false,
            sdkConnected: true,
            connectionError: false
        });

        this.props.CreateToast({
            message: "Connected to host",
            postmessage: "You are now connected to the streaming session."
        });
    }

    handleConnectionError(error) {
        console.log("Guest SDK connection error:", error);
        this.setState({
            isLoading: false,
            sdkConnected: false,
            connectionError: true
        });

        this.props.CreateToast({
            message: "Connection issue detected",
            postmessage: "We'll try to reconnect you automatically."
        });
    }

    componentDidMount() {
        this.waitForHostSession();

        // this.props.SubscribeToCustom((msg) => {
        //     var data = JSON.parse(msg);
        //     if (data.targetuser == "All" || data.targetuser == socket.id) {
        //         if (data.actiontype == "unlock") {
        //             this.props.CreateToast({ message: "Now you have control" })
        //             this.setState({
        //                 lock: false
        //             })
        //         }
        //         if (data.actiontype == "lock") {
        //             this.props.CreateToast({ message: "Your control has been revoked by host" })
        //             this.setState({
        //                 lock: true
        //             })
        //         }
        //     } else {
        //         this.setState({
        //             lock: true
        //         })
        //     }
        // })
    }
    componentDidUpdate(prevProps, prevState) {
      // Handle session ending notification
      if (this.props.isNearingEnd && !prevProps.isNearingEnd && !this.state.hasShownEndingToast) {
          this.props.CreateToast({
              message: "Session Ending Soon",
              postmessage: "The session will end in less than 5 minutes.",
          });
          this.setState({ hasShownEndingToast: true });
      }

      // Log when client data changes
      if (prevProps.clientData !== this.props.clientData && this.props.clientData) {
          console.log("Guest client data updated:", this.props.clientData);

          if (this.props.clientData.data && this.props.clientData.data.name) {
              console.log("Guest name updated to:", this.props.clientData.data.name);
          }
      }
  }

  componentWillUnmount() {
      // No cleanup needed for SDK as CloudStreamingViewGuest handles its own cleanup
  }
    render() {
        console.log("Guest: Rendering Lark component",);
        // Check if we're still waiting for host session or getting auth code
        if (this.state.isGettingAuthCode || (!this.state.authCode && !this.state.connectionError)) {
            return (
                <div style={{
                    position: "absolute",
                    width: "100%",
                    height: "100%",
                    top: 0,
                    left: 0,
                    backgroundColor: "rgba(0, 0, 0, 0.7)",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                }}>
                    <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mb-4"></div>
                    <p className="text-white text-xl font-semibold">Waiting for Host...</p>
                    <p className="text-gray-300 mt-2">Please wait while the host establishes the session</p>
                    <p className="text-gray-400 text-sm mt-6">This may take a moment as the host sets up LarkXR</p>
                </div>
            );
        }

        // Check if we have SDK ID, auth code and application ID
        if ( !this.state.authCode || !this.state.applicationId) {
            return (
                <div style={{
                    position: "absolute",
                    width: "100%",
                    height: "100%",
                    top: 0,
                    left: 0,
                    backgroundColor: "rgba(0, 0, 0, 0.7)",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                }}>
                    <div className="text-red-500 text-6xl mb-4">⚠️</div>
                    <p className="text-white text-xl font-semibold">Session Not Ready</p>
                    <p className="text-gray-300 mt-2">Host has not established the session yet</p>
                    <button
                        onClick={() => {
                            this.setState({ connectionError: false });
                            this.waitForHostSession();
                        }}
                        className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    >
                        Retry Connection
                    </button>
                </div>
            );
        }

        // We have everything we need - render the CloudStreamingViewGuest
        const applicationId = this.state.applicationId;
        const authCode = this.state.authCode;

        // Get the guest name from clientData
        let guestName = "guest";
        if (this.props.clientData && this.props.clientData.data && this.props.clientData.data.name) {
            guestName = this.props.clientData.data.name;
        }

        return (
            <CloudStreamingViewGuest
                authCode={authCode}
                applicationId={applicationId}
                guestName={guestName}
                onConnectionSuccess={this.handleConnectionSuccess}
                onConnectionError={this.handleConnectionError}
            />
        );
    }
}
const mapStateToProps = state => {
    return {
        reduxConfigDetails: state.Sessions.configData,
        clientData: state.Call.ClientData,
        ProjectDetails: state.Sessions.projectDetails,
    }
}
const mapDispatchTothisprops = {
    ...HostActions,
    ...ExceptionActions,
    ...Sessions
}

export default connect(mapStateToProps, mapDispatchTothisprops)(Lark)