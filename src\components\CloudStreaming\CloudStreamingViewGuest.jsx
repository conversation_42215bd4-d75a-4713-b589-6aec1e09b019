// src/components/CloudStreaming/CloudStreamingViewGuest.jsx
import React from "react";
import { LarkSR } from "larksr_websdk";
import PxyWebCommonUI from 'pxy_webcommonui';
import JoystickBottomImage from '../../assets/img/mobile/joy_stick_bottom.png';
import JoystickTopImage from '../../assets/img/mobile/joy_stick_top.png';

const { Joystick, Capabilities, Keyboard } = PxyWebCommonUI;

export default class CloudStreamingViewGuest extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      remoteReady: false,
      isLoading: true,
      connectionError: false,
    }
    this.myRef = React.createRef();
    this.uiContainerRef = React.createRef();
    this.uiKeyboardRef = React.createRef();
  }

  componentDidMount() {
    console.log("Guest: CloudStreamingViewGuest mounted with props:", this.props);
    // Only initialize if we have valid SDK ID, auth code and application ID
    if (this.props.authCode && this.props.applicationId) {
      this.initializeLarkSR();
    } else {
      this.setState({
        isLoading: true,
        connectionError: false
      });
    }
  }

  componentDidUpdate(prevProps) {
    // Initialize if we just received valid props for the first time
    if (( !prevProps.authCode || !prevProps.applicationId) &&
        this.props.authCode && this.props.applicationId) {
      this.setState({
        isLoading: true,
        connectionError: false
      });
      this.initializeLarkSR();
      return;
    }

    // Reinitialize if SDK ID, auth code or application ID changes (and all are valid)
    if ((prevProps.authCode !== this.props.authCode ||
         prevProps.applicationId !== this.props.applicationId) &&
        this.props.authCode && this.props.applicationId) {

      this.cleanup();
      this.setState({
        isLoading: true,
        connectionError: false
      });
      this.initializeLarkSR();
    }
  }

  initializeLarkSR() {
    const larksr = new LarkSR({
      rootElement: this.myRef.current,
      serverAddress: "https://ps.propvr.io:8181/",
      useSeparateMediaSharePeer: true,
    });

    const sdkId = "2b8edf14c2e348ab8211a742af4d5a15";
    const authCode = this.props.authCode;
    const applicationId = this.props.applicationId;
    const guestName = this.props.guestName || "guest";
    if (!sdkId || !authCode || !applicationId) {
      console.error("Guest: Missing required props: sdkId, authCode or applicationId");
      this.setState({ connectionError: true, isLoading: false });
      return;
    }

    // Validate auth code format (should be at least 8 characters)
    if (authCode.length < 8) {
      const error = `Guest: Invalid auth code format. Expected at least 8 characters, got ${authCode.length}`;
      console.error(error);
      this.setState({
        connectionError: error,
        isLoading: false
      });
      return;
    }

    console.log("Guest: Attempting LarkSR connection with sdkId:", sdkId, "and authCode:", authCode);

    // Initialize SDK with SDK ID (same as host)
    larksr.initSDKAuthCode(sdkId)
      .then(() => {
        console.log("Guest: SDK initialized successfully with sdkId:", sdkId);
        // Connect to the same session as host using auth code
        return larksr.connect({
          appliId: applicationId,
          playerMode: 1, // Guest mode (0 = host, 1 = guest)
          userType: 0,   // Guest user type
          nickname: guestName,
          authCode: authCode // Use auth code for connection
        });
      })
      .then(() => {
        console.log("Guest: LarkSR connected successfully");
        this.setState({
          isLoading: false,
          connectionError: false
        });

        // Notify parent component of successful connection if callback provided
        if (this.props.onConnectionSuccess) {
          this.props.onConnectionSuccess();
        }
      })
      .catch((e) => {
        console.error("Guest: LarkSR connection error:", e);
        console.error("Guest: Error details:", {
          message: e.message,
          code: e.code,
          sdkId: sdkId,
          authCode: authCode,
          applicationId: applicationId
        });

        const errorMessage = e.message || JSON.stringify(e);
        this.setState({
          connectionError: `Guest connection failed: ${errorMessage}`,
          isLoading: false
        });

        // Notify parent component of connection error if callback provided
        if (this.props.onConnectionError) {
          this.props.onConnectionError(e);
        }
      });

    // Set up event listeners
    this.setupEventListeners(larksr);
    this.larksr = larksr;

    // Initialize mobile controls if needed
    if (Capabilities.isMobile) {
      this.initializeMobileControls();
    }
  }

  setupEventListeners(larksr) {
    const events = [
      'connect', 'gotremotesteam', 'meidaloaded',
      'mediaplaysuccess', 'mediaplayfailed', 'meidaplaymute',
      'error', 'info', 'apprequestinput', 'resourcenotenough'
    ];

    events.forEach(event => {
      larksr.on(event, this.handleLarkEvent(event));
    });
  }

  initializeMobileControls() {
    if (this.uiContainerRef.current) {
      this.joystick = new Joystick({
        container: this.uiContainerRef.current,
        bottomImage: JoystickBottomImage,
        topImage: JoystickTopImage,
        onMove: (data) => {
          if (this.larksr) {
            this.larksr.sendGamepadInput({
              type: 'joystick',
              data: data
            });
          }
        }
      });
    }

    if (this.uiKeyboardRef.current) {
      this.keyboard = new Keyboard({
        container: this.uiKeyboardRef.current,
        onKeyPress: (key) => {
          if (this.larksr) {
            this.larksr.sendKeyboardInput(key);
          }
        }
      });
    }
  }

  handleLarkEvent = (eventName) => (e) => {
    console.log(`Guest LarkSR ${eventName} Event:`, e);

    switch(eventName) {
      case 'connect':
        console.log("Guest: LarkSR connected event received");
        this.setState({
          isLoading: false,
          connectionError: false
        });
        if (this.props.onConnectionSuccess) {
          this.props.onConnectionSuccess();
        }
        break;
      case 'meidaloaded':
        console.log("Guest: Media loaded successfully");
        this.setState({ remoteReady: true });
        break;
      case 'mediaplaysuccess':
        console.log("Guest: Media play success");
        this.joystick?.show();
        this.setState({ isLoading: false });
        break;
      case 'error':
        console.error("Guest: LarkSR error event:", e);
        const errorMessage = e.message || JSON.stringify(e);
        this.setState({
          connectionError: `Guest error: ${errorMessage}`,
          isLoading: false
        });
        if (this.props.onConnectionError) {
          this.props.onConnectionError(e);
        }
        break;
      case 'resourcenotenough':
        console.error("Guest: Resource not enough");
        this.setState({
          connectionError: "Resource not enough - please try again later",
          isLoading: false
        });
        break;
    }
  }

  cleanup() {
    if (this.joystick) {
      this.joystick.destroy();
      this.joystick = null;
    }
    if (this.keyboard) {
      this.keyboard.destroy();
      this.keyboard = null;
    }
    if (this.larksr) {
      this.larksr.destroy();
      this.larksr = null;
    }
  }

  render() {
    return (
      <div ref={this.myRef} style={{ position: 'relative', width: '100%', height: '100%' }}>
        <div ref={this.uiContainerRef} 
          style={{
            position: 'absolute',
            zIndex: 2000
          }}
        />
        <div ref={this.uiKeyboardRef} 
          style={{
            position: 'absolute',
            zIndex: 2000,
            bottom: 0,
            width: '100%'
          }}
        />
        
        {/* Loading overlay */}
        {this.state.isLoading && (
          <div style={{
            position: "absolute",
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            backgroundColor: "rgba(0, 0, 0, 0.7)",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 3000
          }}>
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mb-4"></div>
            <p className="text-white text-xl font-semibold">Connecting to Host...</p>
            <p className="text-gray-300 mt-2">Please wait while we establish the connection</p>
          </div>
        )}

        {/* Error overlay */}
        {this.state.connectionError && !this.state.isLoading && (
          <div style={{
            position: "absolute",
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            backgroundColor: "rgba(0, 0, 0, 0.8)",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 3000
          }}>
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <p className="text-white text-xl font-semibold">Connection Error</p>
            <p className="text-gray-300 mt-2">Unable to connect to the streaming session</p>
            <button 
              onClick={() => {
                this.setState({ connectionError: false, isLoading: true });
                this.initializeLarkSR();
              }}
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Retry Connection
            </button>
          </div>
        )}
      </div>
    );
  }

  componentWillUnmount() {
    this.cleanup();
  }
}
